#![allow(unused)]
use std::ops::Deref;
use std::sync::Arc;
use std::time::Duration;

use color_eyre::Result;
use color_eyre::eyre::{Context as _, bail, eyre};
use futures::StreamExt;
use prost::Message;
use steam_vent::auth::{
    AuthConfirmationHandler as _, ConsoleAuthConfirmationHandler, DeviceConfirmationHandler,
    FileGuardDataStore,
};
use steam_vent::proto::MsgKind;
use steam_vent::proto::steammessages_clientserver_login::{CMsgClientLogOff, CMsgClientLoggedOff};
use steam_vent::{
    Connection, ConnectionTrait, GameCoordinator, NetworkError, RawNetMessage, ServerList,
    UntypedMessage,
};
use tracing::{debug, error, info, instrument, warn};
use valveprotos::deadlock::{CMsgGcToClientPartyEvent, CsoCitadelLobby, CsoCitadelParty};
use valveprotos::gcsdk::{CMsgConnectionStatus, CMsgSoCacheSubscribed};

use crate::bot::bot_utils::deadlock_startup_seq;
use crate::utils;

mod bot_utils;
pub mod pool_stats;

#[derive(Debug, Clone)]
pub struct InvokePayload {
    pub kind: i32,
    pub data: Vec<u8>,
    pub job_cooldown_millis: u32,
    pub rate_limit_cooldown_millis: Option<u32>,
    pub bot_in_all_groups: Option<Vec<String>>,
    pub bot_in_any_groups: Option<Vec<String>>,
    pub bot_username: Option<String>,
}

#[derive(Debug, Default, Clone)]
pub struct BotConfig {
    pub username: String,
    pub password: String,
    pub proxy: Option<String>,
    pub groups: Vec<String>,
}

#[derive(Debug)]
pub struct BotConn {
    conn: Connection,
    gc: GameCoordinator,
    pub bot_name: String,
}

impl BotConn {
    async fn invoke_inner(
        &self,
        payload: InvokePayload,
    ) -> std::result::Result<RawNetMessage, NetworkError> {
        debug!("Invoking job of kind: {}", payload.kind);
        let msg = UntypedMessage(payload.data);

        let val = self
            .gc
            .job_untyped(msg, MsgKind(payload.kind), true)
            .await?;

        Ok(val)
    }
    #[instrument(skip_all, fields(bot_name = self.bot_name, kind = payload.kind))]
    pub async fn invoke(
        &self,
        payload: InvokePayload,
    ) -> std::result::Result<RawNetMessage, NetworkError> {
        self.invoke_inner(payload).await
    }

    #[instrument(skip(self, payload), fields(bot_name = self.bot_name, kind = payload.kind))]
    pub async fn invoke_with_retries(
        &self,
        payload: &InvokePayload,
        max_retries: i32,
    ) -> std::result::Result<RawNetMessage, NetworkError> {
        let mut retries = 0;

        loop {
            match self.invoke_inner(payload.clone()).await {
                Ok(v) => return Ok(v),
                Err(e) => {
                    retries += 1;

                    if retries > max_retries {
                        debug!("Failing due to exceeding max retries");
                        return Err(e);
                    }

                    let sleep_time_s = 2 * retries;
                    let sleep_dur = Duration::from_secs(sleep_time_s as u64);
                    debug!(
                        "Retrying after got error during invoke: {:?}. Attempt #{}. Sleeping for \
                         {}s",
                        e, retries, sleep_time_s
                    );

                    tokio::time::sleep(sleep_dur).await;
                }
            }
        }
    }

    /// Logoff
    #[instrument(skip(self), fields(bot_name = self.bot_name))]
    pub async fn disconnect(&self) -> Result<()> {
        let logoff_msg = CMsgClientLogOff {
            ..Default::default()
        };
        self.conn.send(logoff_msg).await?;
        match tokio::time::timeout(
            Duration::from_secs(10),
            self.conn.one::<CMsgClientLoggedOff>(),
        )
        .await
        {
            Ok(Ok(v)) => {
                debug!("Got CMsgClientLoggedOff result: {}", v.eresult());
            }
            Ok(Err(e)) => {
                warn!("Error logging off: {:?}", e);
                return Err(e.into());
            }
            Err(_) => {
                warn!(
                    "Never received LoggedOff response - probably internet or fundamental \
                     connection issue"
                );
                return Err(eyre!("Never received LoggedOff response"));
            }
        }

        debug!("Successfully logged off");

        Ok(())
    }
}

#[instrument(skip_all)]
pub async fn create_bot(cfg: &BotConfig) -> Result<BotConn> {
    let server_list = ServerList::discover_with(DiscoverOptions::default().with_cell(184)).await?;
    let mut connection = Connection::login_with_proxy(
        &server_list,
        &cfg.username,
        &cfg.password,
        FileGuardDataStore::user_cache(),
        ConsoleAuthConfirmationHandler::default().or(DeviceConfirmationHandler),
        cfg.proxy.clone(),
    )
    .await?;

    connection.set_timeout(Duration::from_secs(10));

    let game_coordinator = GameCoordinator::new_without_startup(&connection, 1422450).await?;

    deadlock_startup_seq(&connection, &game_coordinator)
        .await
        .context("failed startup seq")?;

    let mut connection_status_subscriber = game_coordinator.filter.on_kind(MsgKind(24));
    let account_name = cfg.username.clone();
    tokio::spawn(async move {
        while let Ok(msg) = connection_status_subscriber.recv().await {
            if let Ok(data) = CMsgSoCacheSubscribed::decode(msg.data) {
                for obj in data.objects {
                    let Some(data) = obj.object_data.first() else {
                        continue;
                    };
                    if let Ok(event) = CsoCitadelLobby::decode(data.as_ref()) {
                        debug!("Lobby event: {:?}", event);
                        let Some(match_id) = event.match_id else {
                            continue;
                        };
                        let Ok(party_id) =
                            utils::get_party_id_from_account(account_name.clone()).await
                        else {
                            continue;
                        };
                        match utils::send_match_id_callback(&party_id, match_id).await {
                            Ok(()) => info!("Match ID Callback sent"),
                            Err(e) => warn!("Failed to send match id callback: {e}"),
                        }
                        match utils::store_party_id_match_id_in_redis(&party_id, match_id).await {
                            Ok(()) => info!("Match ID stored in Redis"),
                            Err(e) => warn!("Failed to store Match ID: {e}"),
                        }
                        continue;
                    }
                    if let Ok(event) = CsoCitadelParty::decode(data.as_ref()) {
                        debug!("Party event: {:?}", event);
                        match utils::send_party_settings_callback(&event).await {
                            Ok(()) => info!("Settings Callback sent"),
                            Err(e) => warn!("Failed to send settings callback: {e}"),
                        }
                        let Some(account_id) = event.members.first().and_then(|m| m.account_id)
                        else {
                            continue;
                        };
                        let Some(party_id) = event.party_id else {
                            continue;
                        };
                        let Some(join_code) = event.join_code else {
                            continue;
                        };
                        let party_code = match utils::format_number_custom_base(join_code) {
                            Ok(p) => p,
                            Err(e) => {
                                warn!(e);
                                continue;
                            }
                        };
                        match utils::store_account_party_code_in_redis(
                            party_id,
                            account_name.clone(),
                            account_id,
                            party_code,
                        )
                        .await
                        {
                            Ok(()) => info!("Party Code stored in Redis"),
                            Err(e) => warn!("Failed to store Party Code: {e}"),
                        }
                    }
                }
            }
        }
    });

    let bc = BotConn {
        conn: connection,
        gc: game_coordinator,
        bot_name: cfg.username.clone(),
    };
    debug!(account = cfg.username, "Bot connected!");
    Ok(bc)
}
