services:
  sv-proxy:
    build: .
    ports:
      - 4246:4246
    volumes:
      - ./config:/data/config
    environment:
      CONFIG_PATH: /data/config/config.yaml
      HEALTH_MIN_ALIVE_BOTS: 200
      HEALTH_MIN_ALIVE_BOTS_PERCENT: 50
    init: true
    logging:
      driver: "json-file"
      options:
        max-size: "50m"    # Maximum size of each log file
        max-file: "3"      # Maximum number of log files to keep
        compress: "true"   # Compress rotated log files
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4246/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 2m
