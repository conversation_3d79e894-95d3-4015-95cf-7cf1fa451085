use std::pin::pin;
use std::time::Duration;

use color_eyre::Result;
use futures::StreamExt;
use futures::future::select;
use prost::Message as _;
use steam_vent::proto::MsgKind;
use steam_vent::proto::enums_clientserver::EMsg;
use steam_vent::proto::steammessages_clientserver::CMsgClientGamesPlayed;
use steam_vent::proto::steammessages_clientserver::cmsg_client_games_played::GamePlayed;
use steam_vent::{Connection, ConnectionTrait as _, GameCoordinator, UntypedMessage};
use tokio::time::sleep;
use tracing::{debug, info, warn};
use valveprotos::deadlock::{CMsgCitadelClientHello, ECitadelRegionMode, EgcCitadelClientMessages};
use valveprotos::gcsdk::CMsgConnectionStatus;

pub(super) async fn deadlock_startup_seq(
    connection: &Connection,
    gc: &GameCoordinator,
) -> Result<()> {
    connection
        .send_with_kind(
            CMsgClientGamesPlayed {
                games_played: vec![GamePlayed {
                    game_id: Some(1422450_u64),
                    ..Default::default()
                }],
                ..Default::default()
            },
            EMsg::k_EMsgClientGamesPlayedWithDataBlob,
        )
        .await?;

    sleep(Duration::from_secs(2)).await;

    // let welcome = gc.wait_welcome();
    let welcome_playtest = async {
        match gc
            .filter
            .one_kind(MsgKind(
                EgcCitadelClientMessages::KEMsgGcToClientDevPlaytestStatus as i32,
            ))
            .await
        {
            Ok(_) => {
                debug!("Got playtest");
                Ok(())
            }
            Err(e) => Err(e),
        }
    };
    let mut connection_status_subscriber = gc.filter.on_kind(MsgKind(4009));
    let connection_status_handle = tokio::spawn(async move {
        while let Ok(msg) = connection_status_subscriber.recv().await {
            let data = msg.data;
            let Ok(msg) = CMsgConnectionStatus::decode(data) else {
                warn!("Got invalid game server message");
                continue;
            };
            debug!("Got connection status: {:?}", msg);
        }
    });
    let hello_sender = async {
        loop {
            let hello_msg = CMsgCitadelClientHello {};

            let encoded = UntypedMessage(hello_msg.encode_to_vec());

            debug!("Sending hello");
            if let Err(e) = gc.send_untyped(encoded, MsgKind(4006), true).await {
                return Result::<(), _>::Err(e);
            }
            sleep(Duration::from_secs(5)).await;
        }
    };

    select(pin!(welcome_playtest), pin!(hello_sender)).await;

    Ok(())
}
