#![forbid(unsafe_code)]
#![deny(clippy::all)]
#![deny(unreachable_pub)]
#![deny(clippy::pedantic)]
#![allow(clippy::missing_errors_doc)]
#![allow(clippy::unreadable_literal)]
#![allow(clippy::cast_sign_loss)]
#![allow(clippy::cast_precision_loss)]
#![allow(clippy::cast_possible_truncation)]

use std::error::Error;
use std::process;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::time::Duration;

use api::run_api_server;
use color_eyre::Result;
use tokio::sync::oneshot;
use tokio::task::JoinSet;
use tracing::{debug, info, warn};
use tracing_subscriber::EnvFilter;

use crate::bot_manager::{BotManager, execute_bot_refresh};
use crate::config::{GConfig, PROXY_CONFIG};
use crate::utils::get_current_client_version;

pub mod api;
pub mod bot;
mod bot_manager;
pub mod config;
mod metrics_exporter;
pub mod utils;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    tracing_subscriber::fmt()
        .with_env_filter(EnvFilter::try_from_default_env().unwrap_or_else(|_| {
            EnvFilter::new("debug,h2=warn,hyper_util=warn,reqwest=warn,rustls=warn,steam_vent=info")
        })) // Reads RUST_LOG env variable
        .init();

    let config_path = std::env::var("CONFIG_PATH").expect("CONFIG_PATH must be set");
    let json_contents = tokio::fs::read_to_string(config_path).await?;
    GConfig::update_from_yaml(&json_contents)?;

    let bot_manager = Arc::new(BotManager::new());
    {
        let cfg = PROXY_CONFIG.load_full();

        for (_, acc) in &cfg.accounts {
            bot_manager
                .add_bot(bot::BotConfig {
                    username: acc.username.clone(),
                    password: acc.password.clone(),
                    proxy: acc.proxy.clone(),
                    groups: acc.groups.clone(),
                })
                .await?;
        }
    }

    let mut background_job_set = JoinSet::new();
    background_job_set.spawn({
        let bot_manager = bot_manager.clone();
        async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            loop {
                interval.tick().await;
                debug!("Executing bot refresh job");
                execute_bot_refresh(bot_manager.clone()).await;
            }
        }
    });

    // Spawn client version check job
    let (shutdown_tx, shutdown_rx) = oneshot::channel();
    let shutdown_flag = Arc::new(AtomicBool::new(false));
    background_job_set.spawn({
        let shutdown_flag = shutdown_flag.clone();
        async move {
            let mut interval = tokio::time::interval(Duration::from_secs(120)); // Check every 2 minutes
            let mut current_version: Option<u32> = None;

            loop {
                interval.tick().await;
                debug!("Checking client version");

                match get_current_client_version().await {
                    Ok(Some(version)) => {
                        debug!("Current client version: {}", version);

                        if let Some(prev_version) = current_version {
                            if prev_version < version {
                                info!(
                                    "Client version changed from {} to {}",
                                    prev_version, version
                                );
                                info!("Initiating shutdown to restart with new client version");

                                // Set the shutdown flag to true
                                shutdown_flag.store(true, Ordering::SeqCst);

                                // Send shutdown signal
                                if let Err(e) = shutdown_tx.send(()) {
                                    warn!("Failed to send shutdown signal: {:?}", e);
                                }

                                break;
                            }
                        } else {
                            info!("Initial client version: {}", version);
                        }

                        current_version = Some(version);
                    }
                    Ok(None) => warn!("Failed to get client version"),
                    Err(e) => warn!("Failed to get client version: {:?}", e),
                }
            }
        }
    });

    // Run API server with shutdown signal
    let server_result = tokio::select! {
        result = run_api_server(bot_manager.clone()) => result,
        _ = shutdown_rx => {
            info!("Received shutdown signal due to client version change");
            Ok(())
        }
    };

    // Shutdown the background jobs
    background_job_set.shutdown().await;

    // If shutdown was triggered by version change, exit with code 0 to allow service manager to restart
    if shutdown_flag.load(Ordering::SeqCst) {
        info!("Exiting due to client version change. Service manager should restart the process.");
        process::exit(0);
    }

    Ok(server_result?)
}
