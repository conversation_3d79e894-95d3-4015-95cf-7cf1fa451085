use std::time::{Duration, Instant};

use poem_openapi::Object;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct CooldownBotInfo {
    pub username: String,
    pub ready_at: Instant,
    pub until_ready: Duration,
}
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ReadyBotInfo {
    pub username: String,
    pub became_ready_at: Option<Instant>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Object)]
pub struct BotStateInfo {
    pub username: String,
    pub state_desc: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct BotSegmentStats {
    pub total_bots: u32,
    pub total_alive_bots: u32,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct PoolKindInfo {
    pub all_stats: BotSegmentStats,
    pub group_stats: BotSegmentStats,
    pub total_ready_bots: u32,
    pub all_bots_states: Vec<BotStateInfo>,
    pub group_bots_states: Vec<BotStateInfo>,

    pub ready_bots: Vec<ReadyBotInfo>,
    pub cooldown_bots: Vec<CooldownBotInfo>,
}

#[derive(Debug, <PERSON>lone, Object)]
pub struct PoolInfo {
    pub total_bots: u32,
    pub total_alive_bots: u32,
    pub all_bots_states: Vec<BotStateInfo>,
}
