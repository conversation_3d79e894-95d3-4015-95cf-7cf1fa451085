use metrics_exporter_prometheus::{PrometheusBuilder, PrometheusHandle};
use poem::{Endpoint, IntoEndpoint, Request, Response};
use reqwest::{Method, StatusCode};

pub(crate) struct PrometheusExporter {
    handle: PrometheusHandle,
}

impl PrometheusExporter {
    /// Create a `PrometheusExporter` endpoint.
    pub(crate) fn new() -> Self {
        let builder = PrometheusBuilder::new();
        let handle = builder
            .install_recorder()
            .expect("failed to install recorder");
        Self { handle }
    }
}

impl IntoEndpoint for PrometheusExporter {
    type Endpoint = PrometheusExporterEndpoint;

    fn into_endpoint(self) -> Self::Endpoint {
        PrometheusExporterEndpoint {
            handle: self.handle.clone(),
        }
    }
}

#[doc(hidden)]
pub(crate) struct PrometheusExporterEndpoint {
    handle: PrometheusHandle,
}

impl Endpoint for PrometheusExporterEndpoint {
    type Output = Response;

    async fn call(&self, req: Request) -> poem::Result<Self::Output> {
        if req.method() != Method::GET {
            return Ok(StatusCode::METHOD_NOT_ALLOWED.into());
        }

        let content = self.handle.render();
        Ok(Response::builder()
            .header("Content-Type", "text/plain")
            .body(content))
    }
}
