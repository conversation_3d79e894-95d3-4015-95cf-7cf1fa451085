FROM rust:1.89.0-slim-trixie AS chef

RUN apt-get update && apt-get install -y protobuf-compiler sccache
RUN cargo install --locked cargo-chef
ENV RUSTC_WRAPPER=sccache SCCACHE_DIR=/sccache

WORKDIR /app

FROM chef AS planner
COPY . .
RUN cargo chef prepare --recipe-path recipe.json

FROM chef AS builder
WORKDIR /app
COPY --from=planner /app/recipe.json recipe.json
RUN --mount=type=cache,target=/usr/local/cargo/registry \
  --mount=type=cache,target=/usr/local/cargo/git \
  --mount=type=cache,target=$SCCACHE_DIR,sharing=locked \
  cargo chef cook --release --recipe-path recipe.json
COPY . .
RUN --mount=type=cache,target=/usr/local/cargo/registry \
  --mount=type=cache,target=/usr/local/cargo/git \
  --mount=type=cache,target=$SCCACHE_DIR,sharing=locked \
  cargo build --release --bin sv-proxy

# We do not need the Rust toolchain to run the binary!
FROM debian:trixie-slim AS runtime
WORKDIR /app
COPY --from=builder /app/target/release/sv-proxy /usr/local/bin

RUN apt-get update && apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

ENTRYPOINT ["/usr/local/bin/sv-proxy"]
