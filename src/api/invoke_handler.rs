use std::sync::Arc;

use base64::prelude::*;
use color_eyre::owo_colors::OwoColorize as _;
use poem::error::BadRequest;
use poem::web::Data;
use poem_openapi::payload::J<PERSON>;
use poem_openapi::{ApiResponse, Object, OpenApi};
use redis::{AsyncCommands, Client as RedisClient};
use tracing::{debug, error, info, warn};

use crate::api::{BearerScheme, gate_bearer};
use crate::bot::InvokePayload;
use crate::bot::pool_stats::PoolKindInfo;
use crate::bot_manager::{BotManager, InvokeError};

pub(super) struct InvokeApi;

/// Rate limit info about daily requests for an account
#[derive(Debug, Object)]
struct DailyRateLimit {
    /// The number of requests used today for this account and cooldown key
    requests_used: u32,
    /// The maximum number of requests allowed per day for this account and cooldown key
    requests_limit: u32,
    /// The number of requests remaining for today
    requests_remaining: u32,
    /// Time in seconds until the bucket resets (seconds until midnight UTC)
    reset_time_seconds: u32,
}

/// Rate limit info about the pool of accounts, including the suggested
/// wait time until the next request, in order to smooth out across the rate limit
/// period.
#[derive(Debug, Object)]
struct PoolLimitInfo {
    /// The total number of "alive" bots in this group that could possibly serve this request
    total_group_alive_bots: u32,
    /// The total number of bots that could serve this request which are not on cooldown
    ready_bots: u32,

    /// Of all bots which are not ready, the duration in milliseconds before they become
    /// ready again. Mostly useful for debugging and observability purposes.
    cooldown_bots_available_in: Vec<u32>,

    /// The time to wait for, in milliseconds, until sending the next request of the same kind
    wait_for_millis: u32,
}

impl PoolLimitInfo {
    pub(crate) fn new_from_pool_kind_info(info: &PoolKindInfo) -> Self {
        Self {
            total_group_alive_bots: info.group_stats.total_alive_bots,
            ready_bots: info.total_ready_bots,
            cooldown_bots_available_in: info
                .cooldown_bots
                .clone()
                .into_iter()
                .map(|x| x.until_ready.as_millis() as u32)
                .collect(),

            wait_for_millis: match info.group_stats.total_alive_bots {
                0 => 10_000,
                1.. => 1000, // Default 1 second between requests
            },
        }
    }
}

#[derive(Debug, Object)]
struct ValidInvokeResponse {
    /// Base64-encoded string of binary protobuf data
    data: String,
    username: String,
    pool_limit_info: PoolLimitInfo,
    daily_rate_limit: DailyRateLimit,
}

#[derive(Debug, Object)]
struct ErrorInvokeResponse {
    message: String,
    pool_limit_info: Option<PoolLimitInfo>,
    daily_rate_limit: Option<DailyRateLimit>,
}

#[derive(ApiResponse, Debug)]
enum InvokeResponse {
    #[oai(status = 200)]
    Ok(Json<ValidInvokeResponse>),

    /// The bot's request failed even after tries, and has been put into a reset queue.
    /// Retrying may remedy the issue.
    #[oai(status = 502)]
    ErrorFailedRequest(Json<ErrorInvokeResponse>),

    /// No ready accounts to serve the request, because they're on cooldown.
    #[oai(status = 429)]
    ErrorRateLimit(Json<ErrorInvokeResponse>),

    /// Daily rate limit exceeded for this account and cooldown key
    #[oai(status = 429)]
    ErrorDailyRateLimit(Json<ErrorInvokeResponse>),

    /// No alive accounts to serve the request, likely recovering after some time.
    #[oai(status = 503)]
    ErrorAccountsUnavailable(Json<ErrorInvokeResponse>),

    /// Something went wrong, not as a result of the request
    #[oai(status = 500)]
    ErrorInternal(Json<ErrorInvokeResponse>),
}

#[derive(Object)]
struct InvokeRequest {
    /// Base64-encoded string of binary data
    data: String,

    /// The message "kind" / "type" corresponding to the proto job
    message_kind: i32,

    /// Bot-groups allowed for this request. If all of these groups match the groups of a bot, that
    /// bot is considered allowed.
    ///
    /// Passing no groups is considered matching any bot.
    bot_in_all_groups: Option<Vec<String>>,

    /// Bot-groups allowed for this request. If any of these groups match the groups of a bot, that
    /// bot is considered allowed.
    ///
    /// Passing no groups is considered matching any bot.
    bot_in_any_groups: Option<Vec<String>>,

    /// The username of the bot to use for this request.
    bot_username: Option<String>,

    /// The cooldown key for rate limiting. Each cooldown key has its own daily bucket per account.
    cooldown_key: String,

    /// The maximum number of requests allowed per day for this cooldown key
    max_requests_per_day: u32,
}

/// Redis-based daily rate limiter
struct DailyRateLimiter {
    redis_client: RedisClient,
}

impl DailyRateLimiter {
    pub fn new(redis_url: &str) -> Result<Self, redis::RedisError> {
        let redis_client = RedisClient::open(redis_url)?;
        Ok(Self { redis_client })
    }

    /// Check and increment the daily rate limit for a given account and cooldown key
    /// Returns the current usage info or None if limit is exceeded
    pub async fn check_and_increment(
        &self,
        account: &str,
        cooldown_key: &str,
        max_requests: u32,
    ) -> Result<Option<DailyRateLimit>, redis::RedisError> {
        let mut conn = self.redis_client.get_multiplexed_async_connection().await?;

        // Create Redis key for this account and cooldown key for today
        let today = chrono::Utc::now().format("%Y-%m-%d").to_string();
        let key = format!("daily_limit:{}:{}:{}", account, cooldown_key, today);

        // Use Redis transaction to atomically check and increment
        let (current_count,): (u32,) = redis::pipe()
            .atomic()
            .incr(&key, 1)
            .expire(&key, 86400) // Expire at end of day (24 hours)
            .ignore() // Ignore the EXPIRE result
            .query_async(&mut conn)
            .await?;

        let seconds_until_midnight = {
            let now = chrono::Utc::now();
            let tomorrow = now.date_naive().succ_opt().unwrap().and_hms_opt(0, 0, 0).unwrap();
            let tomorrow_utc = chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(tomorrow, chrono::Utc);
            (tomorrow_utc - now).num_seconds() as u32
        };

        if current_count > max_requests {
            // Decrement back since we exceeded the limit
            let _: () = conn.decr(&key, 1).await?;

            Ok(Some(DailyRateLimit {
                requests_used: max_requests,
                requests_limit: max_requests,
                requests_remaining: 0,
                reset_time_seconds: seconds_until_midnight,
            }))
        } else {
            Ok(Some(DailyRateLimit {
                requests_used: current_count,
                requests_limit: max_requests,
                requests_remaining: max_requests.saturating_sub(current_count),
                reset_time_seconds: seconds_until_midnight,
            }))
        }
    }

    /// Get current usage without incrementing
    pub async fn get_usage(
        &self,
        account: &str,
        cooldown_key: &str,
        max_requests: u32,
    ) -> Result<DailyRateLimit, redis::RedisError> {
        let mut conn = self.redis_client.get_multiplexed_async_connection().await?;

        let today = chrono::Utc::now().format("%Y-%m-%d").to_string();
        let key = format!("daily_limit:{}:{}:{}", account, cooldown_key, today);

        let current_count: u32 = conn.get(&key).await.unwrap_or(0);

        let seconds_until_midnight = {
            let now = chrono::Utc::now();
            let tomorrow = now.date_naive().succ_opt().unwrap().and_hms_opt(0, 0, 0).unwrap();
            let tomorrow_utc = chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(tomorrow, chrono::Utc);
            (tomorrow_utc - now).num_seconds() as u32
        };

        Ok(DailyRateLimit {
            requests_used: current_count,
            requests_limit: max_requests,
            requests_remaining: max_requests.saturating_sub(current_count),
            reset_time_seconds: seconds_until_midnight,
        })
    }
}

#[OpenApi]
impl InvokeApi {
    #[allow(clippy::too_many_lines)]
    #[oai(path = "/invoke", method = "post")]
    async fn invoke(
        &self,
        bot_manager: Data<&Arc<BotManager>>,
        req: Json<InvokeRequest>,
        auth: BearerScheme,
    ) -> poem::Result<InvokeResponse> {
        gate_bearer(auth)?;

        let data_decoded = BASE64_STANDARD.decode(&req.data).map_err(BadRequest)?;
        let request_size = data_decoded.len();

        // Initialize Redis rate limiter (you may want to inject this as a dependency instead)
        let rate_limiter = match DailyRateLimiter::new("redis://localhost:6379") {
            Ok(limiter) => limiter,
            Err(e) => {
                error!("Failed to connect to Redis: {}", e);
                return Ok(InvokeResponse::ErrorInternal(Json(ErrorInvokeResponse {
                    message: "Failed to connect to rate limiting service".to_string(),
                    pool_limit_info: None,
                    daily_rate_limit: None,
                })));
            }
        };

        // For requests with specific bot username, check their daily rate limit
        if let Some(username) = &req.bot_username {
            match rate_limiter
                .check_and_increment(username, &req.cooldown_key, req.max_requests_per_day)
                .await
            {
                Ok(Some(daily_limit)) => {
                    if daily_limit.requests_remaining == 0 && daily_limit.requests_used >= daily_limit.requests_limit {
                        return Ok(InvokeResponse::ErrorDailyRateLimit(Json(ErrorInvokeResponse {
                            message: format!("Daily rate limit exceeded for account {} and cooldown key {}. Limit: {}, Used: {}",
                                username, req.cooldown_key, daily_limit.requests_limit, daily_limit.requests_used),
                            pool_limit_info: None,
                            daily_rate_limit: Some(daily_limit),
                        })));
                    }
                },
                Ok(None) => {
                    return Ok(InvokeResponse::ErrorDailyRateLimit(Json(ErrorInvokeResponse {
                        message: format!("Daily rate limit exceeded for account {} and cooldown key {}", username, req.cooldown_key),
                        pool_limit_info: None,
                        daily_rate_limit: None,
                    })));
                },
                Err(e) => {
                    error!("Redis error checking rate limit: {}", e);
                    return Ok(InvokeResponse::ErrorInternal(Json(ErrorInvokeResponse {
                        message: "Rate limiting service error".to_string(),
                        pool_limit_info: None,
                        daily_rate_limit: None,
                    })));
                }
            }
        }

        let response = bot_manager
            .invoke(InvokePayload {
                kind: req.message_kind,
                data: data_decoded,
                bot_in_all_groups: req.bot_in_all_groups.clone(),
                bot_in_any_groups: req.bot_in_any_groups.clone(),
                job_cooldown_millis: 1000, // Default 1 second cooldown
                rate_limit_cooldown_millis: req.rate_limit_cooldown_millis,
                bot_username: req.bot_username.clone(),
            })
            .await;

        let response = match response {
            Ok(value) => value,
            Err(e) => match e {
                InvokeError::NoReadyAccount { kind: _, pool_info } => {
                    let pool_limit_info = PoolLimitInfo::new_from_pool_kind_info(&pool_info);
                    return Ok(if pool_limit_info.total_group_alive_bots == 0 {
                        InvokeResponse::ErrorAccountsUnavailable(Json(ErrorInvokeResponse {
                            message: "No alive accounts to serve the request".into(),
                            pool_limit_info: Some(pool_limit_info),
                            daily_rate_limit: None,
                        }))
                    } else {
                        {
                            let kind = req.message_kind;
                            let wait_for_ms = pool_limit_info.wait_for_millis;
                            let ready_bots = pool_limit_info.ready_bots;
                            let alive_bots = pool_limit_info.total_group_alive_bots;
                            let total_bots = pool_info.group_stats.total_bots;
                            let dead_bots = pool_info.group_stats.total_bots
                                - pool_limit_info.total_group_alive_bots;

                            warn!(
                                %wait_for_ms, %ready_bots, %dead_bots, %alive_bots, %total_bots,
                                "[{kind}] Rejected Invoke kind={kind} req_size={request_size}",
                            );
                        }
                        InvokeResponse::ErrorRateLimit(Json(ErrorInvokeResponse {
                            message: "All available bots are currently on cooldown for this message kind".into(),
                            pool_limit_info: Some(pool_limit_info),
                            daily_rate_limit: None,
                        }))
                    });
                }
                InvokeError::JobRequestFailure {
                    source: _,
                    picked_username: _,
                    pool_info,
                } => {
                    let pool_limit_info = PoolLimitInfo::new_from_pool_kind_info(&pool_info);
                    return Ok(InvokeResponse::ErrorFailedRequest(Json(
                        ErrorInvokeResponse {
                            message: "The bot failed to execute the job even after multiple retries.".into(),
                            pool_limit_info: Some(pool_limit_info),
                            daily_rate_limit: None,
                        },
                    )));
                }
                InvokeError::InternalFailure {
                    message,
                    picked_username: _,
                    pool_info,
                } => {
                    let pool_limit_info = PoolLimitInfo::new_from_pool_kind_info(&pool_info);
                    return Ok(InvokeResponse::ErrorInternal(Json(ErrorInvokeResponse {
                        message,
                        pool_limit_info: Some(pool_limit_info),
                        daily_rate_limit: None,
                    })));
                }
            },
        };

        let updated_pool_info = bot_manager
            .fetch_pool_kind_info(
                req.message_kind,
                req.bot_in_all_groups.as_ref(),
                req.bot_in_any_groups.as_ref(),
                1000, // Default 1 second cooldown
            )
            .await;
        let pool_limit_info = PoolLimitInfo::new_from_pool_kind_info(&updated_pool_info);

        // Get the daily rate limit info for the account that was actually used
        let daily_rate_limit = match rate_limiter
            .get_usage(&response.picked_username, &req.cooldown_key, req.max_requests_per_day)
            .await
        {
            Ok(limit) => limit,
            Err(e) => {
                error!("Failed to get daily rate limit usage: {}", e);
                // Return a default/empty rate limit info rather than failing the request
                DailyRateLimit {
                    requests_used: 0,
                    requests_limit: req.max_requests_per_day,
                    requests_remaining: req.max_requests_per_day,
                    reset_time_seconds: 86400, // 24 hours
                }
            }
        };

        {
            let kind = req.message_kind;
            let used_account = response.picked_username.clone();
            let response_size = response.message.data.len();
            let wait_for_ms = pool_limit_info.wait_for_millis;
            let ready_bots = pool_limit_info.ready_bots;
            let alive_bots = pool_limit_info.total_group_alive_bots;
            let total_bots = updated_pool_info.group_stats.total_bots;
            let dead_bots = updated_pool_info.group_stats.total_bots
                - updated_pool_info.group_stats.total_alive_bots;
            let cooldown_key = &req.cooldown_key;
            let daily_used = daily_rate_limit.requests_used;
            let daily_limit = daily_rate_limit.requests_limit;

            info!(%kind, %request_size, %response_size, %used_account, %wait_for_ms, %ready_bots, %dead_bots, %alive_bots, %total_bots, %cooldown_key, %daily_used, %daily_limit, "[{}] Executed Invoke", kind.bold());
            debug!(
                %wait_for_ms, %ready_bots, %dead_bots, %alive_bots, %total_bots, %cooldown_key, %daily_used, %daily_limit,
                "[{kind}] Executed Invoke kind={kind} req_size={request_size} res_size={response_size} used_account={used_account}",
            );
        }

        Ok(InvokeResponse::Ok(Json(ValidInvokeResponse {
            data: BASE64_STANDARD.encode(&response.message.data),
            username: response.picked_username,
            pool_limit_info,
            daily_rate_limit,
        })))
    }
}
pub(super) fn wait_for_dynamic_adjustment(
    total: u32,
    ready_count: u32,
    per_bot_period_millis: u32,
    until_ready: &[u32],
) -> u32 {
    let total = total.max(1);
    let ready = ready_count.min(total);

    let target_interval = ((per_bot_period_millis / total) as f32 * 0.7) as u32;

    let size = (until_ready.len() / 2) as u32;
    let denom = (size + ready).max(1);
    let avg_until = until_ready.iter().take(size as usize).sum::<u32>() / denom;
    let result = avg_until
        .checked_div((ready as f32 / 1.5) as u32)
        .unwrap_or(avg_until);

    target_interval.max(result)
}

// /// Dynamically adjusts a given wait_for_millis period, based on the number of total and eligible
// /// bots available, and a target "flow rate" (the amount of concurrently used bots).
// ///
// /// This makes it so that even with concurrent callers, as long as all of them are well-behaved,
// /// rate limits are unlikely to be hit and the flow rate will be optimized without large bursts.
// pub fn wait_for_dynamic_adjustment_2(
//     total: u32,
//     eligible: u32,
//     per_bot_period_millis: u32,
//     until_ready_next_millis: u32,
//     mut target_flow_rate: f32,
// ) -> u32 {
//     // Ensure we don't divide by zero and eligible doesn't exceed total
//     let total = total.max(1);
//     let eligible = eligible.min(total);

//     // Change ideal flow rate to fit a discreet # of bots
//     target_flow_rate = (total as f32 * target_flow_rate).floor() / total as f32;

//     let wait_for_millis = per_bot_period_millis / total;

//     // Calculate current flow rate (ratio of bots in use)
//     let flow_rate = 1.0 - (eligible as f32 / total as f32);

//     // Calculate how far we are from ideal flow rate
//     let flow_rate_difference = flow_rate - target_flow_rate;

//     // Start with basic adjustment
//     let mut flow_rate_adjustment = flow_rate_difference;

//     // If we're above target flow rate (more bots in use than desired),
//     // add aggressive adjustment based on how far we are above ideal
//     if flow_rate_difference >= 0.0 {
//         flow_rate_adjustment += (1.0 - target_flow_rate) / (1.0 - (flow_rate + 0.10).min(0.99));
//     // if the flow rate is within 10 points of the target, just use the base rate limit to avoid
//     // bouncing around
//     } else if flow_rate_difference > -0.10 {
//         flow_rate_adjustment = 0.0;
//     } else {
//         // Speed up is more dangerous than slowdown, so we reduce speed up in general
//         flow_rate_adjustment /= 2.0;
//     }

//     // Calculate final adjustment in milliseconds
//     let adjustment = flow_rate_adjustment * wait_for_millis as f32;

//     // Apply adjustment to base wait time and ensure it's a positive integer
//     wait_for_millis
//         .saturating_add_signed(adjustment.floor() as i32)
//         .min(60_000) // Fail safe - max wait for a minute. This helps avoid weird behavior during
//                      // startup when the numbers can get very big.
// }
