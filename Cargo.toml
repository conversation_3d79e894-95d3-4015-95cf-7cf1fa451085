[package]
name = "sv-proxy"
version = "0.1.0"
edition = "2024"

[dependencies]
color-eyre = "0.6.5"
futures = "0.3.31"
indexmap = { version = "2.10.0", features = ["serde"] }
reqwest = { version = "0.12.23", default-features = false, features = ["blocking", "charset", "cookies", "gzip", "http2", "json", "rustls-tls", "zstd"] }
serde = { version = "1.0.219", features = ["derive"] }
thiserror = "2.0.15"
tokio = { version = "1.47.1", features = ["full"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["ansi", "env-filter", "tracing-log"], default-features = false }
steam-vent = { git = "https://github.com/deadlock-api/steam-vent", rev = "b23956106095f08389e2bd7c66f4054992b35d24" }
valveprotos = { git = "https://github.com/deadlock-api/valveprotos-rs", rev = "b807469ae98fd28c5cc6581ca631d0fd5469d172", features = ["gc-common", "gc-client", "serde"] }
prost = "0.14.1"
dashmap = "6.1.0"
variantly = "0.4.0"
arc-swap = "1.7.1"
poem = "3.1.12"
poem-openapi = { version = "5.1.16", features = ["chrono", "static-files", "swagger-ui", "url", "uuid"] }
base64 = "0.22.1"
serde_yml = "0.0.12"
rand = "0.9.2"
metrics-exporter-prometheus = "0.17.2"
redis = { version = "0.32.5", features = ["tokio-comp"] }
anyhow = "1.0.99"


[package.metadata.cargo-machete]
ignored = ["metrics", "metrics-exporter-prometheus"]
