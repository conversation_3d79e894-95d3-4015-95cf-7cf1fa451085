use std::sync::LazyLock;
use std::time::Duration;

use poem_openapi::__private::serde_json::json;
use redis::{AsyncTypedCommands, RedisResult};
use reqwest::Url;
use valveprotos::deadlock::CsoCitadelParty;

const BASE: usize = 30;
const WIDTH: usize = 5;
const ALPHABET: &str = "BCDFGHJKMNPQRSTVWXYZ0123456789";

static REDIS_URL: LazyLock<String> =
    LazyLock::new(|| std::env::var("REDIS_URL").ok().unwrap_or_default());

pub fn format_number_custom_base(num: u64) -> Result<String, String> {
    let mut converted_digits_vec: Vec<char> = Vec::new();
    let mut temp_num = num;

    while temp_num > 0 {
        let remainder = (temp_num % BASE as u64) as usize;
        let digit_char = ALPHABET.chars().nth(remainder).ok_or_else(|| {
            format!("Remainder index {remainder} is out of bounds for the character set string.")
        })?;
        converted_digits_vec.push(digit_char);
        temp_num /= BASE as u64;
    }

    let final_digits_string: String = converted_digits_vec.iter().rev().collect();
    let num_digits_generated = converted_digits_vec.len();

    let mut padding_string = String::new();
    if num_digits_generated < WIDTH {
        let padding_char = ALPHABET.chars().next().ok_or_else(|| {
            "Character set string is empty, cannot get padding character for non-zero num."
                .to_string()
        })?;
        let padding_needed = WIDTH - num_digits_generated;
        padding_string = std::iter::repeat_n(padding_char, padding_needed).collect();
    }

    Ok(format!("{padding_string}{final_digits_string}"))
}

pub async fn get_party_id_from_account(account_name: String) -> RedisResult<String> {
    let redis_url = REDIS_URL.clone();
    let mut redis_client = redis::Client::open(redis_url)?
        .get_multiplexed_async_connection()
        .await?;
    redis::cmd("GET")
        .arg(account_name)
        .query_async(&mut redis_client)
        .await
}

pub async fn store_account_party_code_in_redis(
    party_id: u64,
    account_name: String,
    account_id: u32,
    party_code: String,
) -> RedisResult<()> {
    let redis_url = REDIS_URL.clone();
    let mut redis_client = redis::Client::open(redis_url)?
        .get_multiplexed_async_connection()
        .await?;

    let account_party_code = format!("{}:{}:{}", &account_name, account_id, party_code);
    redis::pipe()
        .set_ex(party_id.to_string(), account_party_code, 24 * 60 * 60)
        .set_ex(account_name, party_id.to_string(), 24 * 60 * 60)
        .exec_async(&mut redis_client) // Execute the pipeline
        .await
}

pub async fn send_match_id_callback(party_id: &str, match_id: u64) -> anyhow::Result<()> {
    let redis_url = REDIS_URL.clone();
    let mut redis_client = redis::Client::open(redis_url)?
        .get_multiplexed_async_connection()
        .await?;
    let callback_url = redis_client
        .get(format!("{party_id}:callback-url"))
        .await?
        .ok_or(anyhow::anyhow!("No callback url"))?;
    let callback_secret = redis_client
        .get(format!("{party_id}:callback-secret"))
        .await?
        .ok_or(anyhow::anyhow!("No callback secret"))?;
    Ok(reqwest::Client::new()
        .post(callback_url)
        .header("X-Callback-Secret", callback_secret)
        .json(&json!({ "match_id": match_id }))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .and_then(reqwest::Response::error_for_status)
        .map(|_| ())?)
}

pub async fn send_party_settings_callback(event: &CsoCitadelParty) -> anyhow::Result<()> {
    let redis_url = REDIS_URL.clone();
    let mut redis_client = redis::Client::open(redis_url)?
        .get_multiplexed_async_connection()
        .await?;
    let party_id = event.party_id.ok_or(anyhow::anyhow!("No party id"))?;
    let callback_url = redis_client
        .get(format!("{party_id}:callback-url"))
        .await?
        .ok_or(anyhow::anyhow!("No callback url"))?;
    let callback_secret = redis_client
        .get(format!("{party_id}:callback-secret"))
        .await?
        .ok_or(anyhow::anyhow!("No callback secret"))?;
    let mut callback_url = Url::parse(&callback_url)?;
    // add "settings" to the path
    callback_url
        .path_segments_mut()
        .map_err(|()| anyhow::anyhow!("Invalid callback url"))?
        .push("settings");
    Ok(reqwest::Client::new()
        .post(callback_url)
        .header("X-Callback-Secret", callback_secret)
        .json(&event)
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .and_then(reqwest::Response::error_for_status)
        .map(|_| ())?)
}

pub async fn store_party_id_match_id_in_redis(party_id: &str, match_id: u64) -> RedisResult<()> {
    let redis_url = REDIS_URL.clone();
    let mut redis_client = redis::Client::open(redis_url)?
        .get_multiplexed_async_connection()
        .await?;
    redis::pipe()
        .set_ex(
            format!("{party_id}:match-id"),
            match_id.to_string(),
            24 * 60 * 60,
        )
        .exec_async(&mut redis_client) // Execute the pipeline
        .await
}

pub async fn get_current_client_version() -> reqwest::Result<Option<u32>> {
    let steam_info = reqwest::get("https://raw.githubusercontent.com/SteamDatabase/GameTracking-Deadlock/refs/heads/master/game/citadel/steam.inf")
        .await
        .and_then(reqwest::Response::error_for_status)?.text().await?;

    for line in steam_info.lines() {
        if line.starts_with("ClientVersion=") {
            return Ok(line.split('=').nth(1).and_then(|v| v.parse().ok()));
        }
    }
    Ok(None)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_number_custom_base_samples() {
        let samples = [
            (16744407, "00G67"),
            (18065467, "2ND2K"),
            (15211152, "Y3QMR"),
            (21886482, "7BYNR"),
            (20973028, "563P8"),
            (14419118, "X4CKM"),
            (8389713, "PP17F"),
            (15309019, "Y7BBZ"),
            (8388579, "PP0ZN"),
            (10414581, "R5121"),
            (12804461, "V4KHQ"),
            (17987414, "2JJBT"),
            (1207555, "CT115"),
        ];

        for (input, expected) in samples {
            let result = format_number_custom_base(input).unwrap();
            assert_eq!(result, expected, "Failed for input: {input}");
        }
    }

    #[tokio::test]
    async fn test_get_current_client_version() {
        let ver = get_current_client_version()
            .await
            .expect("Should be OK")
            .expect("Should be Some");

        assert!(ver > 5600);
    }
}
