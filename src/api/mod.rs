mod invoke_handler;

use std::sync::Arc;
use std::time::Instant;

use color_eyre::owo_colors::OwoColorize;
use poem::listener::TcpListener;
use poem::web::Data;
use poem::{Endpoint as _, EndpointExt, Route};
use poem_openapi::auth::Bearer;
use poem_openapi::payload::{Json, PlainText};
use poem_openapi::{ApiResponse, OpenApi, OpenApiService, SecurityScheme};
use reqwest::StatusCode;
use tracing::{debug, info};

use crate::api::invoke_handler::InvokeApi;
use crate::bot_manager::{BotManager, execute_bot_refresh};
use crate::config::PROXY_CONFIG;
use crate::metrics_exporter::PrometheusExporter;

#[derive(SecurityScheme)]
#[oai(ty = "bearer")]
pub(crate) struct BearerScheme(Bearer);

pub(crate) fn gate_bearer(auth: BearerScheme) -> poem::Result<()> {
    let provided_token = auth.0.token;
    let cfg = PROXY_CONFIG.load();
    for (_, key) in &cfg.keys {
        if provided_token == key.key {
            return Ok(());
        }
    }
    Err(poem::Error::from_string(
        "Invalid auth token",
        StatusCode::UNAUTHORIZED,
    ))
}

#[derive(ApiResponse, Debug)]
enum PoolInfoResponse {
    #[oai(status = 200)]
    Ok(Json<crate::bot::pool_stats::PoolInfo>),
}

struct Api;

#[OpenApi]
impl Api {
    #[oai(path = "/do-reload", method = "post")]
    async fn do_reload(
        &self,
        bot_manager: Data<&Arc<BotManager>>,
        auth: BearerScheme,
    ) -> poem::Result<PlainText<String>> {
        gate_bearer(auth)?;
        execute_bot_refresh(bot_manager.0.clone()).await;
        Ok(PlainText("All done".to_string()))
    }

    #[oai(path = "/pool-info", method = "get")]
    async fn get_pool_info(
        &self,
        bot_manager: Data<&Arc<BotManager>>,
        auth: BearerScheme,
    ) -> poem::Result<PoolInfoResponse> {
        gate_bearer(auth)?;
        let pool_info = bot_manager.fetch_pool_info().await;
        Ok(PoolInfoResponse::Ok(Json(pool_info)))
    }

    #[oai(path = "/health", method = "get")]
    async fn get_health(
        &self,
        bot_manager: Data<&Arc<BotManager>>,
    ) -> poem::Result<PlainText<String>> {
        let info = bot_manager.fetch_pool_info().await;

        // Check if there are enough alive bots
        let min_alive_bots: Option<u32> = std::env::var("HEALTH_MIN_ALIVE_BOTS")
            .ok()
            .and_then(|v| v.parse().ok());
        if let Some(min_alive_bots) = min_alive_bots
            && info.total_alive_bots < min_alive_bots
        {
            return Err(poem::Error::from_string(
                format!(
                    "Not enough alive bots: {} < {}",
                    info.total_alive_bots, min_alive_bots
                ),
                StatusCode::SERVICE_UNAVAILABLE,
            ));
        }

        // Check if there are enough alive bots percent
        let min_alive_bots_percent: Option<f64> = std::env::var("HEALTH_MIN_ALIVE_BOTS_PERCENT")
            .ok()
            .and_then(|v| v.parse().ok());
        if let Some(min_alive_bots_percent) = min_alive_bots_percent {
            let percent = f64::from(info.total_alive_bots) / f64::from(info.total_bots) * 100.0;
            if percent < min_alive_bots_percent {
                return Err(poem::Error::from_string(
                    format!(
                        "Not enough alive bots percent: {percent:.2}% < {min_alive_bots_percent}%"
                    ),
                    StatusCode::SERVICE_UNAVAILABLE,
                ));
            }
        }

        Ok(PlainText("All good".to_string()))
    }
}

pub async fn run_api_server(bot_manager: Arc<BotManager>) -> Result<(), std::io::Error> {
    let api_service =
        OpenApiService::new((Api, InvokeApi), "Deadlock SV Proxy", "1.0").server("/api");
    let ui = api_service.swagger_ui();
    let spec = api_service.spec();
    let app = Route::new()
        .nest("/api", api_service)
        .nest("/metrics", PrometheusExporter::new())
        .nest("/", ui)
        .at("/doc", poem::endpoint::make_sync(move |_| spec.clone()))
        .data(bot_manager)
        .around(|ep, req| async move {
            let uri = req.uri().clone();

            let start_t = Instant::now();
            let method = req.method().clone();
            debug!("HTTP: >> {} {}", method, uri);

            let resp = ep.get_response(req).await;
            let duration = Instant::now().duration_since(start_t);
            let millis = duration.as_millis();

            let resp_n = resp.status().as_u16();
            let status_f = match resp_n {
                200..=299 => resp_n.green().to_string(),
                300..=399 => resp_n.blue().to_string(),
                400..=499 => resp_n.yellow().to_string(),
                500..=599 => resp_n.red().to_string(),
                _ => resp_n.to_string(),
            };

            debug!("HTTP: << {} {} {} {}ms", method, uri, status_f, millis);

            Ok(resp)
        });

    info!("Starting API server on 0.0.0.0:4246");

    poem::Server::new(TcpListener::bind("0.0.0.0:4246"))
        .run(app)
        .await
}
