
name: sv-proxy
services:
  sv-proxy:
    build: ..
    ports:
      - 4246:4246
    volumes:
      - ./config:/data/config
    environment:
      CONFIG_PATH: /data/config/config.yaml
      VIRTUAL_HOST: sv-proxy.plants.sh
      VIRTUAL_PORT: 4246
      LETSENCRYPT_HOST: sv-proxy.plants.sh
    init: true
    logging:
      driver: "json-file"
      options:
        max-size: "50m"    # Maximum size of each log file
        max-file: "3"      # Maximum number of log files to keep
        compress: "true"   # Compress rotated log files
    networks:
      - webserver
    restart: unless-stopped
networks:
  webserver:
    external: true

