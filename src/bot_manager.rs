#![allow(unused)]
use std::fmt;
use std::fmt::Debug;
use std::ops::Deref;
use std::sync::{Arc, LazyLock};
use std::time::{Duration, Instant};

use color_eyre::eyre::{Context, bail, eyre};
use dashmap::DashMap;
use dashmap::try_result::TryResult;
use futures::future::join;
use indexmap::IndexMap;
use rand::rng;
use rand::seq::{IndexedRandom, SliceRandom};
use steam_vent::{NetworkError, RawNetMessage};
use thiserror::Error;
use tokio::sync::{Mutex, MutexGuard, RwLock, Semaphore};
use tokio::task::JoinSet;
use tokio::time::timeout;
use tracing::{debug, error, info, instrument, trace, warn};

use crate::bot::pool_stats::{
    BotSegmentStats, BotStateInfo, CooldownBotInfo, PoolInfo, PoolKindInfo, ReadyBotInfo,
};
use crate::bot::{BotConfig, BotConn, InvokePayload, create_bot};

static CONCURRENT_CONNECTS: LazyLock<usize> = LazyLock::new(|| {
    std::env::var("CONCURRENT_CONNECTS")
        .ok()
        .and_then(|x| x.parse().ok())
        .unwrap_or(100)
});
static CONNECT_TIMEOUT: LazyLock<u64> = LazyLock::new(|| {
    std::env::var("CONNECT_TIMEOUT")
        .ok()
        .and_then(|x| x.parse().ok())
        .unwrap_or(60)
});

#[derive(Error, Debug)]
pub enum InvokeError {
    #[error("no ready account found for kind {kind}.")]
    NoReadyAccount { kind: i32, pool_info: PoolKindInfo },
    // #[error("steam NetworkError: {0}")]
    // NetworkError(#[from] steam_vent::NetworkError),
    #[error("job failed: {source}")]
    JobRequestFailure {
        source: NetworkError,
        picked_username: String,
        pool_info: PoolKindInfo,
    },

    #[error("something went wrong internally: {message}")]
    InternalFailure {
        message: String,
        picked_username: String,
        pool_info: PoolKindInfo,
    },
}

#[derive(Debug)]
enum PickData {
    Available {
        picked_username: String,
        info: PoolKindInfo,
    },

    Unavailable {
        info: PoolKindInfo,
    },
}

pub struct InvokeResponse {
    pub message: RawNetMessage,
    pub picked_username: String,
}

#[allow(clippy::missing_fields_in_debug)]
impl Debug for InvokeResponse {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("InvokeResponse")
            .field("picked_username", &self.picked_username)
            .finish()
    }
}

#[derive(Debug, variantly::Variantly)]
pub enum BotState {
    Uninitialized,
    FailedLevel1 { failed_at: Instant },
    FailedLevel2 { failed_at: Instant },
    FailedLevel3 { failed_at: Instant },
    StagedForRemoval(Option<BotConn>),
    Alive(BotConn),
}
impl fmt::Display for BotState {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match *self {
            BotState::Uninitialized => write!(f, "Uninitialized"),
            BotState::FailedLevel1 { failed_at } => write!(f, "FailedLevel1"),
            BotState::FailedLevel2 { failed_at } => write!(f, "FailedLevel2"),
            BotState::FailedLevel3 { failed_at } => write!(f, "FailedLevel3"),
            BotState::StagedForRemoval(_) => write!(f, "StagedForRemoval"),
            BotState::Alive(_) => write!(f, "Alive"),
        }
    }
}

#[derive(Debug)]
enum BotRemedyActions {
    Initialize,
    AttemptFixLevel1,
    AttemptFixLevel2,
    AttemptFixLevel3,
    Remove,
    Nothing,
}

#[derive(Debug)]
pub struct BotHolder {
    pub cfg: BotConfig,
    pub cooldowns: DashMap<String, Instant>,
    pub state: RwLock<BotState>,
}

#[derive(Debug, Default)]
pub struct BotManager {
    bots: Mutex<IndexMap<String, Arc<BotHolder>>>,
}

impl BotManager {
    pub fn new() -> BotManager {
        BotManager {
            bots: Mutex::new(IndexMap::new()),
        }
    }
    pub async fn add_bot(&self, cfg: BotConfig) -> color_eyre::Result<()> {
        {
            let bots = self.bots.lock().await;
            let key = &cfg.username;
            if bots.contains_key(key) {
                bail!("Already has bot!");
            }
        }

        self.bots.lock().await.insert(
            cfg.username.clone(),
            Arc::new(BotHolder {
                cfg,
                cooldowns: DashMap::new(),
                state: RwLock::new(BotState::Uninitialized),
            }),
        );

        Ok(())
    }

    #[allow(clippy::too_many_lines)]
    pub async fn invoke(&self, payload: InvokePayload) -> Result<InvokeResponse, InvokeError> {
        if let Some(username) = &payload.bot_username {
            return match self.bots.lock().await.get(username) {
                Some(bot) => match &*bot.state.read().await {
                    BotState::Alive(conn) => conn
                        .invoke_with_retries(&payload, 3)
                        .await
                        .map(|res| InvokeResponse {
                            message: res,
                            picked_username: username.clone(),
                        })
                        .map_err(|e| {
                            error!(
                                account = &username,
                                "Got invoke error even after multiple retries: {:?}", e
                            );
                            InvokeError::JobRequestFailure {
                                source: e,
                                picked_username: username.clone(),
                                pool_info: PoolKindInfo::default(),
                            }
                        }),
                    _ => Err(InvokeError::NoReadyAccount {
                        kind: payload.kind,
                        pool_info: PoolKindInfo::default(),
                    }),
                },
                None => Err(InvokeError::NoReadyAccount {
                    kind: payload.kind,
                    pool_info: PoolKindInfo::default(),
                }),
            };
        }

        let (username, pool_info) = match self
            .pick_bot(
                payload.kind,
                payload.job_cooldown_millis,
                payload.bot_in_all_groups.as_ref(),
                payload.bot_in_any_groups.as_ref(),
            )
            .await
        {
            PickData::Available {
                picked_username,
                info,
            } => (picked_username, info),
            PickData::Unavailable { info: pool_info } => {
                return Err(InvokeError::NoReadyAccount {
                    kind: payload.kind,
                    pool_info,
                });
            }
        };

        let bot = {
            let bots = self.bots.lock().await;
            bots.get(&username).cloned()
        };
        if let Some(bot) = bot {
            let res = {
                let state = bot.state.read().await;
                if let BotState::Alive(conn) = &*state {
                    conn.invoke_with_retries(&payload, 2).await
                } else {
                    return Err(InvokeError::InternalFailure {
                        message: "Bot wasn't alive though we thought it should be".into(),
                        picked_username: username,
                        pool_info,
                    });
                }
            };
            match res {
                Ok(res) => {
                    if res.data.len() <= 2
                        && res.kind.0 == 9168
                        && let Some(cooldown_millis) = payload.rate_limit_cooldown_millis
                    {
                        warn!(
                            account = &username,
                            "Got a rate limited response, increasing cooldown time"
                        );
                        bot.cooldowns.insert(
                            format!("MSG:{}", payload.kind),
                            Instant::now() + Duration::from_millis(u64::from(cooldown_millis)),
                        );
                    }
                    Ok(InvokeResponse {
                        message: res,
                        picked_username: username,
                    })
                }
                Err(e) => {
                    error!(
                        account = &username,
                        "Got invoke error even after multiple retries: {:?}", e
                    );

                    trace!(account = &username, "Starting bot disconnect...");

                    let ret_err = Err(InvokeError::JobRequestFailure {
                        source: e,
                        picked_username: username.clone(),
                        pool_info,
                    });

                    // Disconnect
                    {
                        let state = bot.state.read().await;
                        let Some(conn) = state.alive_ref() else {
                            warn!(
                                account = &username,
                                "Attempted disconnect but bot already not alive. Not a real issue"
                            );
                            return ret_err;
                        };

                        debug!(account = &username, "Executing bot disconnect");
                        match conn.disconnect().await {
                            Ok(()) => {
                                debug!("Bot logged off");
                            }
                            Err(e) => {
                                warn!("Couldn't finish bot logoff: {}", e);
                            }
                        }
                    }
                    // Close/drop
                    {
                        trace!(account = &username, "Starting bot set to failure");
                        let mut state = bot.state.write().await;
                        if state.is_not_alive() {
                            warn!(
                                account = &username,
                                "After disconnect, bot was already not alive. Weird, but due to \
                                 multiple concurrent failures, and not significant."
                            );
                            return ret_err;
                        }
                        *state = BotState::FailedLevel1 {
                            failed_at: Instant::now(),
                        };
                        warn!("Marked bot {} as FailedLevel1", bot.cfg.username);
                    }

                    ret_err
                }
            }
        } else {
            Err(InvokeError::InternalFailure {
                message: "Bot is none even though it was just returned".into(),
                picked_username: username,
                pool_info,
            })
        }
    }

    pub async fn fetch_pool_kind_info(
        &self,
        kind: i32,
        bot_in_all_groups: Option<&Vec<String>>,
        bot_in_any_groups: Option<&Vec<String>>,
        cooldown_millis: u32,
    ) -> PoolKindInfo {
        let bots = self.bots.lock().await;
        Self::extract_pool_kind_info(
            &bots,
            kind,
            bot_in_all_groups,
            bot_in_any_groups,
            cooldown_millis,
        )
    }
    pub fn extract_pool_kind_info(
        bots: &IndexMap<String, Arc<BotHolder>>,
        kind: i32,
        bot_in_all_groups: Option<&Vec<String>>,
        bot_in_any_groups: Option<&Vec<String>>,
        cooldown_millis: u32,
    ) -> PoolKindInfo {
        let now = Instant::now();
        let cooldown_key = format!("MSG:{kind}");

        let mut info = PoolKindInfo {
            all_stats: BotSegmentStats::default(),
            group_stats: BotSegmentStats::default(),
            total_ready_bots: 0,
            all_bots_states: Vec::new(),
            group_bots_states: Vec::new(),
            ready_bots: Vec::new(),
            cooldown_bots: Vec::new(),
        };

        for (username, bot) in bots {
            let Ok(state) = bot.state.try_read() else {
                warn!("Bot state for {} currently locked.", username);
                continue;
            };

            info.all_stats.total_bots += 1;
            info.all_bots_states.push(BotStateInfo {
                username: username.clone(),
                state_desc: state.to_string(),
            });
            if state.is_alive() {
                info.all_stats.total_alive_bots += 1;
            }

            let matches_all_group = bot_in_all_groups.as_ref().is_none_or(|reg_groups| {
                reg_groups
                    .iter()
                    .all(|req_group| bot.cfg.groups.contains(req_group))
            });

            let matches_any_group = bot_in_any_groups.as_ref().is_none_or(|reg_groups| {
                reg_groups
                    .iter()
                    .any(|req_group| bot.cfg.groups.contains(req_group))
            });

            if matches_all_group && matches_any_group {
                info.group_stats.total_bots += 1;
                info.group_bots_states.push(BotStateInfo {
                    username: username.clone(),
                    state_desc: state.to_string(),
                });
                if state.is_alive() {
                    info.group_stats.total_alive_bots += 1;

                    let cooldown = bot.cooldowns.get(&cooldown_key).as_deref().copied();

                    if let Some(cooldown) = cooldown {
                        if cooldown <= now {
                            info.total_ready_bots += 1;
                            info.ready_bots.push(ReadyBotInfo {
                                username: username.clone(),
                                became_ready_at: Some(cooldown),
                            });
                        } else {
                            info.cooldown_bots.push(CooldownBotInfo {
                                username: username.clone(),
                                ready_at: cooldown,
                                until_ready: cooldown.duration_since(now),
                            });
                        }
                    } else {
                        info.total_ready_bots += 1;
                        info.ready_bots.push(ReadyBotInfo {
                            username: username.clone(),
                            became_ready_at: None,
                        });
                    }
                }
            }
        }
        info.all_bots_states
            .sort_by(|x, y| x.username.cmp(&y.username));
        info.group_bots_states
            .sort_by(|x, y| x.username.cmp(&y.username));
        info.cooldown_bots.sort_by_key(|x| x.until_ready);
        info.ready_bots.sort_by_key(|x| x.became_ready_at);
        info
    }

    pub async fn fetch_pool_info(&self) -> PoolInfo {
        let bots = self.bots.lock().await;
        Self::extract_pool_info(&bots)
    }

    pub fn extract_pool_info(bots: &IndexMap<String, Arc<BotHolder>>) -> PoolInfo {
        let mut info = PoolInfo {
            total_bots: 0,
            total_alive_bots: 0,
            all_bots_states: Vec::new(),
        };
        for (username, bot) in bots {
            let Ok(state) = bot.state.try_read() else {
                warn!("Bot state for {} currently locked.", username);
                continue;
            };
            if state.is_alive() {
                info.total_alive_bots += 1;
            }
            info.total_bots += 1;
            info.all_bots_states.push(BotStateInfo {
                username: username.clone(),
                state_desc: state.to_string(),
            });
        }

        info
    }

    async fn pick_bot(
        &self,
        kind: i32,
        cooldown_millis: u32,
        bot_in_all_groups: Option<&Vec<String>>,
        bot_in_any_groups: Option<&Vec<String>>,
    ) -> PickData {
        let mut bots = self.bots.lock().await;
        let info = Self::extract_pool_kind_info(
            &bots,
            kind,
            bot_in_all_groups,
            bot_in_any_groups,
            cooldown_millis,
        );
        let now = Instant::now();
        let cooldown_key = format!("MSG:{kind}");

        let selected = info.ready_bots.choose(&mut rng());

        // If we have ready bots, randomly select one
        if let Some(selected) = selected {
            let Some(mut bot) = bots.get_mut(&selected.username) else {
                error!(
                    account = &selected.username,
                    "pick_bot: Couldn't get bot as mutable, something went very wrong as we have \
                     an exclusive lock on `bots`."
                );
                return PickData::Unavailable { info };
            };
            bot.cooldowns.insert(
                cooldown_key,
                now + Duration::from_millis(u64::from(cooldown_millis)),
            );

            PickData::Available {
                picked_username: selected.username.clone(),
                info,
            }
        } else {
            PickData::Unavailable { info }
        }
    }

    #[allow(clippy::too_many_lines)]
    #[instrument(name = "RemedyBot", skip(self))]
    async fn try_remedy_bot(&self, account: String) -> color_eyre::Result<()> {
        let now = Instant::now();
        let action = {
            let bot = {
                let bots = self.bots.lock().await;
                bots.get(&account).cloned()
            };
            let Some(bot) = bot else {
                bail!("Can't get bot")
            };

            let state = bot.state.read().await;
            match &*state {
                BotState::Uninitialized => BotRemedyActions::Initialize,
                BotState::FailedLevel1 { failed_at } => {
                    // 1min
                    let failed_cooldown = Duration::from_secs(60);

                    if now.duration_since(*failed_at) > failed_cooldown {
                        BotRemedyActions::AttemptFixLevel1
                    } else {
                        BotRemedyActions::Nothing
                    }
                }
                BotState::FailedLevel2 { failed_at } => {
                    // 30min
                    let failed_cooldown = Duration::from_secs(30 * 60);

                    if now.duration_since(*failed_at) > failed_cooldown {
                        BotRemedyActions::AttemptFixLevel2
                    } else {
                        BotRemedyActions::Nothing
                    }
                }
                BotState::FailedLevel3 { failed_at } => {
                    // 12h
                    let failed_cooldown = Duration::from_secs(12 * 60 * 60);

                    if now.duration_since(*failed_at) > failed_cooldown {
                        BotRemedyActions::AttemptFixLevel3
                    } else {
                        BotRemedyActions::Nothing
                    }
                }
                BotState::StagedForRemoval(_) => BotRemedyActions::Remove,
                BotState::Alive(_) => BotRemedyActions::Nothing,
            }
        };

        match action {
            BotRemedyActions::Initialize => {
                let bot = {
                    let bots = self.bots.lock().await;
                    bots.get(&account).cloned()
                };
                let Some(bot) = bot else {
                    bail!("Can't get bot")
                };

                debug!("Initializing bot");

                match create_bot(&bot.cfg).await {
                    Ok(conn) => {
                        let mut state = bot.state.write().await;
                        *state = BotState::Alive(conn);
                    }
                    Err(e) => {
                        error!(
                            account = &bot.cfg.username,
                            "Error initializing bot {:?}", e
                        );

                        trace!(account = &bot.cfg.username, "Starting bot set to failure");
                        let mut state = bot.state.write().await;
                        *state = BotState::FailedLevel1 { failed_at: now };
                        warn!(account = &bot.cfg.username, "Marked bot as FailedLevel1");
                        return Err(e).context("Error initializing bot");
                    }
                }
            }
            BotRemedyActions::AttemptFixLevel1 => {
                let bot = {
                    let bots = self.bots.lock().await;
                    bots.get(&account).cloned()
                };
                let Some(bot) = bot else {
                    bail!("Can't get bot")
                };

                info!("Fixing bot (level 1)");

                match create_bot(&bot.cfg).await {
                    Ok(conn) => {
                        {
                            let mut state = bot.state.write().await;
                            *state = BotState::Alive(conn);
                        }
                        info!("Fixed a bot from FailedLevel1 -> Alive!");
                    }
                    Err(e) => {
                        warn!(
                            "(AttemptFixLevel1) Error initializing bot {}, downgrading to \
                             FailedLevel2: {:?}",
                            &bot.cfg.username, e
                        );

                        let mut state = bot.state.write().await;
                        *state = BotState::FailedLevel2 { failed_at: now };

                        return Err(e).context("Error fixing level 1 failure");
                    }
                }
            }
            BotRemedyActions::AttemptFixLevel2 => {
                let bot = {
                    let bots = self.bots.lock().await;
                    bots.get(&account).cloned()
                };
                let Some(bot) = bot else {
                    bail!("Can't get bot")
                };

                info!("Fixing bot (level 2)");

                match create_bot(&bot.cfg).await {
                    Ok(conn) => {
                        {
                            let mut state = bot.state.write().await;
                            *state = BotState::Alive(conn);
                        }
                        info!("Fixed a bot from FailedLevel2 -> Alive!");
                    }
                    Err(e) => {
                        error!(
                            "(AttemptFixLevel2) Error initializing bot {}, downgrading to \
                             FailedLevel3: {:?}",
                            &bot.cfg.username, e
                        );

                        let mut state = bot.state.write().await;
                        *state = BotState::FailedLevel2 { failed_at: now };

                        return Err(e).context("Error fixing level 2 failure");
                    }
                }
            }
            BotRemedyActions::AttemptFixLevel3 => {
                let bot = {
                    let bots = self.bots.lock().await;
                    bots.get(&account).cloned()
                };
                let Some(bot) = bot else {
                    bail!("Can't get bot")
                };

                info!("Fixing bot (level 3)");

                match create_bot(&bot.cfg).await {
                    Ok(conn) => {
                        {
                            let mut state = bot.state.write().await;
                            *state = BotState::Alive(conn);
                        }

                        info!("Fixed a bot from FailedLevel3 -> Alive!");
                    }
                    Err(e) => {
                        error!(
                            "(AttemptFixLevel3) Error initializing bot {}, maintaing at \
                             FailedLevel3: {:?}",
                            &bot.cfg.username, e
                        );

                        return Err(e).context("Error fixing level 3 failure");
                    }
                }
            }
            BotRemedyActions::Remove => {
                {
                    let bot = {
                        let bots = self.bots.lock().await;
                        bots.get(&account).cloned()
                    };
                    let Some(bot) = bot else {
                        bail!("Can't get bot")
                    };
                    info!("Removing bot from active duty");
                    // Disconnect
                    let should_drop = {
                        let state = bot.state.read().await;
                        let Some(staged_state) = state.staged_for_removal_ref() else {
                            error!(
                                "Bot was in StagedForRemoval but isn't anymore, this is probably \
                                 a buggy race condition, but isn't problematic."
                            );
                            return Ok(());
                        };
                        if let Some(conn) = staged_state {
                            conn.disconnect().await.ok();
                            true
                        } else {
                            false
                        }
                    };
                    if should_drop {
                        // Close connection/drop
                        {
                            let mut state = bot.state.write().await;
                            *state = BotState::StagedForRemoval(None);
                        }
                    }
                }
                // else {
                //     warn!("Bots couldn't be borrowed as mutable, not removing {}, will try on next iteration.", &account);
                //     bail!("Bots couldn't be borrowed as mutable");
                // };
                let mut bots = self.bots.lock().await;
                bots.shift_remove(&account);
            }
            BotRemedyActions::Nothing => {}
        }
        Ok(())
    }

    async fn list_usernames(&self) -> Vec<String> {
        let bots = self.bots.lock().await;
        bots.keys().map(std::string::ToString::to_string).collect()
    }
}

pub(crate) async fn execute_bot_refresh(bot_manager: Arc<BotManager>) {
    let mut usernames = bot_manager.list_usernames().await;
    {
        usernames.shuffle(&mut rng());
    }
    let semaphore = Arc::new(Semaphore::new(*CONCURRENT_CONNECTS)); // Limit concurrent tasks to 10
    let mut join_set = JoinSet::new();

    for bot_username in usernames {
        // SAFETY: semaphore acquisition is never closed
        let permit = semaphore.clone().acquire_owned().await.unwrap();
        join_set.spawn({
            let me = bot_manager.clone();
            async move {
                // The permit is automatically released when dropped at the end of this scope
                let _permit = permit;
                match timeout(
                    Duration::from_secs(*CONNECT_TIMEOUT),
                    me.try_remedy_bot(bot_username.clone()),
                )
                .await
                {
                    Ok(Ok(())) => {}
                    Ok(Err(e)) => {
                        error!(
                            account = bot_username,
                            "Error remedying bot {}: {}", bot_username, e
                        );
                    }
                    Err(e) => {
                        error!(
                            account = bot_username,
                            "Timeout Error remedying bot {}: {}", bot_username, e
                        );
                    }
                }
            }
        });
    }
    join_set.join_all().await;
}
