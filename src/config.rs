use std::sync::{Arc, LazyLock};

use arc_swap::ArcSwap;
use color_eyre::Result;
use indexmap::IndexMap;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct ApiKeys {
    pub key: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Account {
    pub username: String,
    pub password: String,
    pub proxy: Option<String>,
    #[serde(default)]
    pub groups: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Default, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub keys: IndexMap<String, ApiKeys>,
    pub accounts: IndexMap<String, Account>,
}

pub static PROXY_CONFIG: LazyLock<ArcSwap<ProxyConfig>> = LazyLock::new(|| {
    let value = ProxyConfig::default();
    ArcSwap::from_pointee(value)
});

pub struct GConfig {}

impl GConfig {
    pub fn update_from_yaml(contents: &str) -> Result<()> {
        let cfg: ProxyConfig = serde_yml::from_str(contents)?;

        GConfig::update(cfg);

        Ok(())
    }

    pub fn update(cfg: ProxyConfig) {
        PROXY_CONFIG.store(Arc::new(cfg));
    }
}
