name: docker-image

on:
  workflow_dispatch:
  push:
    branches:
    - 'main'

permissions:
  contents: read
  packages: write
  id-token: write

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v5

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - uses: depot/setup-action@v1
    - uses: depot/build-push-action@v1
      with:
        platforms: linux/arm64
        project: k7kl7zsmcs
        tags: ghcr.io/${{ github.repository }}:latest
        push: true
